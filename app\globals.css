@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated to biotechnology-themed green color palette */
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0.25 0 0); /* Dark gray #374151 */
  --card: oklch(0.96 0.01 142); /* Soft off-white #f0f4f1 */
  --card-foreground: oklch(0.25 0 0); /* Dark gray */
  --popover: oklch(1 0 0); /* White */
  --popover-foreground: oklch(0.25 0 0); /* Dark gray */
  --primary: oklch(0.6 0.15 142); /* Vibrant green #4caf50 */
  --primary-foreground: oklch(1 0 0); /* White */
  --secondary: oklch(0.8 0.08 142); /* Soft green #a5d6a7 */
  --secondary-foreground: oklch(0.25 0 0); /* Dark gray */
  --muted: oklch(0.96 0.01 142); /* Soft off-white */
  --muted-foreground: oklch(0.25 0 0); /* Dark gray */
  --accent: oklch(0.8 0.08 142); /* Soft green accent */
  --accent-foreground: oklch(0.25 0 0); /* Dark gray */
  --destructive: oklch(0.55 0.2 25); /* Red for destructive actions */
  --destructive-foreground: oklch(1 0 0); /* White */
  --border: oklch(0.9 0 0); /* Light border #e0e0e0 */
  --input: oklch(0.98 0 0); /* Input background #f8f9fa */
  --ring: oklch(0.6 0.15 142 / 0.5); /* Green ring with transparency */
  --chart-1: oklch(0.6 0.15 142); /* Primary green */
  --chart-2: oklch(0.8 0.08 142); /* Secondary green */
  --chart-3: oklch(0.9 0 0); /* Light gray */
  --chart-4: oklch(0.96 0.01 142); /* Off-white */
  --chart-5: oklch(0.25 0 0); /* Dark gray */
  --radius: 0.5rem;
  --sidebar: oklch(0.96 0.01 142); /* Soft off-white */
  --sidebar-foreground: oklch(0.25 0 0); /* Dark gray */
  --sidebar-primary: oklch(0.6 0.15 142); /* Primary green */
  --sidebar-primary-foreground: oklch(1 0 0); /* White */
  --sidebar-accent: oklch(0.8 0.08 142); /* Soft green */
  --sidebar-accent-foreground: oklch(0.25 0 0); /* Dark gray */
  --sidebar-border: oklch(0.9 0 0); /* Light border */
  --sidebar-ring: oklch(0.6 0.15 142 / 0.5); /* Green ring */
}

.dark {
  /* Updated dark mode colors to maintain biotechnology theme */
  --background: oklch(0.1 0 0); /* Very dark background */
  --foreground: oklch(0.95 0 0); /* Light text */
  --card: oklch(0.15 0 0); /* Dark card background */
  --card-foreground: oklch(0.95 0 0); /* Light text on cards */
  --popover: oklch(0.15 0 0); /* Dark popover */
  --popover-foreground: oklch(0.95 0 0); /* Light popover text */
  --primary: oklch(0.65 0.15 142); /* Brighter green for dark mode */
  --primary-foreground: oklch(0.1 0 0); /* Dark text on primary */
  --secondary: oklch(0.3 0.08 142); /* Darker secondary green */
  --secondary-foreground: oklch(0.95 0 0); /* Light text */
  --muted: oklch(0.2 0 0); /* Dark muted background */
  --muted-foreground: oklch(0.7 0 0); /* Muted text */
  --accent: oklch(0.3 0.08 142); /* Dark accent green */
  --accent-foreground: oklch(0.95 0 0); /* Light accent text */
  --destructive: oklch(0.5 0.2 25); /* Darker red */
  --destructive-foreground: oklch(0.95 0 0); /* Light text */
  --border: oklch(0.25 0 0); /* Dark border */
  --input: oklch(0.2 0 0); /* Dark input background */
  --ring: oklch(0.65 0.15 142 / 0.5); /* Bright green ring */
  --sidebar: oklch(0.12 0 0); /* Slightly lighter than background */
  --sidebar-foreground: oklch(0.95 0 0); /* Light sidebar text */
  --sidebar-primary: oklch(0.65 0.15 142); /* Bright green */
  --sidebar-primary-foreground: oklch(0.1 0 0); /* Dark text */
  --sidebar-accent: oklch(0.3 0.08 142); /* Dark accent */
  --sidebar-accent-foreground: oklch(0.95 0 0); /* Light text */
  --sidebar-border: oklch(0.25 0 0); /* Dark border */
  --sidebar-ring: oklch(0.65 0.15 142 / 0.5); /* Bright green ring */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
